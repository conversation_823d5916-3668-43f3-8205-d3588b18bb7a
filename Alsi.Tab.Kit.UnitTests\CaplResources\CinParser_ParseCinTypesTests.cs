﻿using Alsi.Common.Parsers.Cin;

namespace Alsi.Tab.Kit.UnitTests.CaplResources;

public class CinParser_ParseCinTypesTests
{
    public CinParser_ParseCinTypesTests()
    {
        CinParser.Initialize();
    }

    [Fact]
    public void ParseToStructure_ShouldParseStructTypesCorrectly()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.ErrorMessage.ShouldBeNullOrEmpty();
        result.CodeBlocks.ShouldNotBeNull();
        result.CodeBlocks.Count.ShouldBeGreaterThan(0);

        var structDefinitionNames = result.CodeBlocks.First().ParsedStructDefinitions.Select(x => x.Name).ToArray();

        var compareStructDefinitionNames = new string[]{
            "StruSIDandSUBID",
            "StruDIDinLevel",
            "StruRoutine",
            "StruRID",
            "StruE2Esig",
            "StruE2EsigGroup",
            "StruE2EsigGroupFlexRay",
            "StruE2Emsg",
            "StruVehicleModeMsg",
            "SlotFormat",
            "StruVehicleModeMsgOnFlexRay",
            "StruE2EmsgFlexRay",
            "StruE2EdtcInfo"
        };

        for (var i = 0; i < structDefinitionNames.Length && i < compareStructDefinitionNames.Length; i++)
        {
            structDefinitionNames[i].ShouldBe(compareStructDefinitionNames[i]);
        }

        structDefinitionNames.Length.ShouldBe(compareStructDefinitionNames.Length);
    }

    [Fact]
    public void ParseToStructure_ShouldParseEnumTypesCorrectly()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.ErrorMessage.ShouldBeNullOrEmpty();
        result.CodeBlocks.ShouldNotBeNull();
        result.CodeBlocks.Count.ShouldBeGreaterThan(0);

        var enumNames = result.CodeBlocks.First().ParsedEnumDefinitions.Select(x => x.Name).ToArray();

        var compareEnumNames = new string[]{
            "VEHICLE_CARMODE",
            "VEHICLE_USAGEMODE",
            "BUS_TYPE"
        };

        for (var i = 0; i < enumNames.Length && i < compareEnumNames.Length; i++)
        {
            enumNames[i].ShouldBe(compareEnumNames[i]);
        }

        enumNames.Length.ShouldBe(compareEnumNames.Length);
    }
}
