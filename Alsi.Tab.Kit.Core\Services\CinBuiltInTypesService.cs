using Alsi.App;
using Alsi.Common.Parsers.Cin;
using Alsi.Common.Parsers.Cin.Models;
using System;
using System.Collections.Generic;
using System.IO;

namespace Alsi.Tab.Kit.Core.Services
{
    public class CinBuiltInTypesService
    {
        private const string CIN_FOLDER = "cin";
        private const string TYPES_FOLDER = "types";

        private static CinStructDefinition[] structDefinitionCache = null;
        private static CinEnumDefinition[] enumDefinitionCache = null;

        private static bool isCached = false;

        public CinStructDefinition[] GetCinStructDefinitions()
        {
            Load();
            return structDefinitionCache;
        }

        public CinEnumDefinition[] GetCinEnumDefinitions()
        {
            Load();
            return enumDefinitionCache;
        }

        private void Load()
        {
            if (!isCached)
            {
                isCached = true;
            }

            var cinStructDefinitions = new List<CinStructDefinition>();
            var cinEnumDefinitions = new List<CinEnumDefinition>();

            var filePaths = Directory.EnumerateFiles(GetCinTypesFolder(), "*.cin");
            foreach (var filePath in filePaths)
            {

                try
                {
                    var cinModel = CinParser.ParseFile(filePath);
                    foreach (var codeBlock in cinModel.CodeBlocks)
                    {
                        cinStructDefinitions.AddRange(codeBlock.ParsedStructDefinitions);
                        cinEnumDefinitions.AddRange(codeBlock.ParsedEnumDefinitions);
                    }
                }
                catch (Exception e)
                {
                    AppEnv.Logger.Error(e, $"Failed to parse: {filePath}");
                }

            }
            enumDefinitionCache = cinEnumDefinitions.ToArray();
            structDefinitionCache = cinStructDefinitions.ToArray();
        }

        private string GetCinTypesFolder()
        {
            return Path.Combine(AppEnv.WebHostApp.EntryAssemblyFolder, CIN_FOLDER, TYPES_FOLDER);
        }

    }
}
