/*
 * 高级模板示例
 * 包含复杂的变量类型和结构体
 */

struct CanMessage
{
  dword id;
  byte dlc;
  byte data[8];
};

struct TestConfig
{
  char name[64];
  int enabled;
  float threshold;
};

variables
{
  // 常量定义
  const int MAX_MESSAGES = 1000;
  const char DEVICE_TYPE[16] = "CANoe";
  const float DEFAULT_TIMEOUT = 10.5;
  
  // 结构体变量
  struct TestConfig gTestConfigs[3] = {
    {"Test1", 1, 1.5},
    {"Test2", 0, 2.0},
    {"Test3", 1, 3.5}
  };
  
  // 复杂数组
  dword gMessageIds[8] = {0x100, 0x200, 0x300, 0x400, 0x500, 0x600, 0x700, 0x800};
  byte gDefaultData[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
  
  // 字符串数组
  char gLogLevels[4][16] = {"ERROR", "WARN", "INFO", "DEBUG"};
  
  // 控制参数
  int gCurrentLogLevel = 2;
  int gMaxRetries = 5;
  dword gHeartbeatInterval = 1000;
}

on start
{
  write("Advanced template started");
  write("Device Type: %s", DEVICE_TYPE);
  write("Max Messages: %d", MAX_MESSAGES);
  write("Default Timeout: %.1f", DEFAULT_TIMEOUT);
  
  write("Test Configurations:");
  int i;
  for (i = 0; i < 3; i++)
  {
    write("  %s: enabled=%d, threshold=%.1f", 
          gTestConfigs[i].name, 
          gTestConfigs[i].enabled, 
          gTestConfigs[i].threshold);
  }
  
  write("Current Log Level: %s", gLogLevels[gCurrentLogLevel]);
  write("Max Retries: %d", gMaxRetries);
  write("Heartbeat Interval: %d ms", gHeartbeatInterval);
}

on timer heartbeat
{
  // 心跳处理
  write("Heartbeat");
}

on key 'r'
{
  write("Reset requested");
  // 重置逻辑
}

on key 'q'
{
  write("Quit requested");
  stop();
}
